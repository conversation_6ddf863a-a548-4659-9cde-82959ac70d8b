2025-06-23T21:29:48.632830Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/.turbo-cookie")}
2025-06-23T21:29:48.632842Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-23T21:29:48.734027Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/1.cookie")}
2025-06-23T21:29:48.734039Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-23T21:29:48.834028Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/2.cookie"), AnchoredSystemPathBuf("packages/core/.turbo/turbo-test.log")}
2025-06-23T21:29:48.834042Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-23T21:30:25.634157Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/.turbo/turbo-test.log")}
2025-06-23T21:30:25.634193Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-06-23T21:30:47.234014Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/index.ts")}
2025-06-23T21:30:47.234038Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-06-23T21:31:05.334026Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/testConfig.ts")}
2025-06-23T21:31:05.334051Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-06-23T21:31:23.233725Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/testConfig.ts")}
2025-06-23T21:31:23.233772Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-06-23T21:32:03.133405Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/RealCredentialsIntegration.test.ts")}
2025-06-23T21:32:03.133427Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-06-23T21:32:35.335488Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/RealCredentialsIntegration.test.ts")}
2025-06-23T21:32:35.335527Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-06-23T21:33:51.232779Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/CoreComponentsValidation.test.ts")}
2025-06-23T21:33:51.232916Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-06-23T21:34:38.231939Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/CoreComponentsValidation.test.ts")}
2025-06-23T21:34:38.231976Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-06-23T21:34:51.831955Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/CoreComponentsValidation.test.ts")}
2025-06-23T21:34:51.832073Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-06-23T21:34:51.951199Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/CoreComponentsValidation.test.ts")}
2025-06-23T21:34:51.951231Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-06-23T21:34:51.951290Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-23T21:35:05.831469Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/CoreComponentsValidation.test.ts")}
2025-06-23T21:35:05.831490Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-06-23T21:35:23.332760Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/CoreComponentsValidation.test.ts")}
2025-06-23T21:35:23.332840Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-06-23T21:35:36.232636Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/CoreComponentsValidation.test.ts")}
2025-06-23T21:35:36.232741Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-06-23T21:35:57.732924Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/CoreComponentsValidation.test.ts")}
2025-06-23T21:35:57.733183Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-06-23T21:37:26.633092Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/FrameworkAdaptersValidation.test.ts")}
2025-06-23T21:37:26.633155Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }}))
