/**
 * @file Core Components Validation Test
 * Tests individual components with real MongoDB Atlas and OpenAI API
 */

import * as dotenv from 'dotenv';
import { MongoClient, Db, ObjectId } from 'mongodb';
import { OpenAIEmbeddingProvider } from '../embeddings/OpenAIEmbeddingProvider';
import { VectorSearchEngine } from '../intelligence/VectorSearchEngine';
import { SemanticMemoryEngine } from '../intelligence/SemanticMemoryEngine';
import { MemoryCollection } from '../collections/MemoryCollection';

// Load environment variables
dotenv.config({ path: '../../../../.env' });

describe('🧠 Universal AI Brain - Core Components Validation', () => {
  let mongoClient: MongoClient;
  let database: Db;
  let embeddingProvider: OpenAIEmbeddingProvider;
  let memoryCollection: MemoryCollection;

  const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://romiluz:<EMAIL>/?retryWrites=true&w=majority&appName=agents';
  const OPENAI_API_KEY = process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************';
  const DATABASE_NAME = 'universal_ai_brain_test';

  beforeAll(async () => {
    console.log('🚀 Setting up Core Components Validation Tests...');
    
    try {
      // Connect to MongoDB Atlas
      mongoClient = new MongoClient(MONGODB_URI, {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 10000,
        socketTimeoutMS: 45000,
        connectTimeoutMS: 10000,
        retryWrites: true,
        retryReads: true
      });

      await mongoClient.connect();
      database = mongoClient.db(DATABASE_NAME);
      console.log('✅ Connected to MongoDB Atlas');

      // Initialize OpenAI Embedding Provider
      embeddingProvider = new OpenAIEmbeddingProvider({
        apiKey: OPENAI_API_KEY,
        model: 'text-embedding-3-small',
        dimensions: 1536
      });
      console.log('✅ OpenAI Embedding Provider initialized');

      // Initialize Memory Collection
      memoryCollection = new MemoryCollection(database, 'test_memory_collection');
      console.log('✅ Memory Collection initialized');

    } catch (error) {
      console.error('❌ Setup failed:', error);
      throw error;
    }
  }, 30000);

  afterAll(async () => {
    console.log('🧹 Cleaning up Core Components Validation Tests...');
    
    try {
      // Clean up test data
      if (database) {
        const collections = await database.listCollections().toArray();
        for (const collection of collections) {
          if (collection.name.includes('test')) {
            await database.collection(collection.name).deleteMany({});
            console.log(`🗑️ Cleaned collection: ${collection.name}`);
          }
        }
      }

      // Close connections
      if (mongoClient) {
        await mongoClient.close();
        console.log('✅ MongoDB connection closed');
      }
    } catch (error) {
      console.warn('⚠️ Cleanup warning:', error);
    }
  }, 30000);

  describe('🔌 MongoDB Atlas Connectivity', () => {
    test('should connect to MongoDB Atlas successfully', async () => {
      expect(mongoClient).toBeDefined();
      expect(database).toBeDefined();
      
      // Test database operations
      const testCollection = database.collection('connectivity_test');
      const testDoc = { test: true, timestamp: new Date() };
      
      const result = await testCollection.insertOne(testDoc);
      expect(result.insertedId).toBeDefined();
      
      const retrieved = await testCollection.findOne({ _id: result.insertedId });
      expect(retrieved).toBeDefined();
      expect(retrieved?.test).toBe(true);
      
      await testCollection.deleteOne({ _id: result.insertedId });
      console.log('✅ MongoDB Atlas connectivity verified');
    });

    test('should create and query collections', async () => {
      const testCollection = database.collection('collection_test');
      
      // Insert multiple documents
      const testDocs = [
        { name: 'Document 1', type: 'test', value: 100 },
        { name: 'Document 2', type: 'test', value: 200 },
        { name: 'Document 3', type: 'production', value: 300 }
      ];
      
      const insertResult = await testCollection.insertMany(testDocs);
      expect(insertResult.insertedCount).toBe(3);
      
      // Query documents
      const testTypeCount = await testCollection.countDocuments({ type: 'test' });
      expect(testTypeCount).toBe(2);
      
      const highValueDocs = await testCollection.find({ value: { $gte: 200 } }).toArray();
      expect(highValueDocs).toHaveLength(2);
      
      // Clean up
      await testCollection.deleteMany({ type: { $in: ['test', 'production'] } });
      console.log('✅ MongoDB collection operations verified');
    });
  });

  describe('🤖 OpenAI Integration', () => {
    test('should generate embeddings with OpenAI API', async () => {
      const testText = 'This is a test for Universal AI Brain embedding generation with real OpenAI API';

      const embedding = await embeddingProvider.generateEmbedding(testText);

      expect(embedding).toBeDefined();
      expect(Array.isArray(embedding)).toBe(true);
      expect(embedding).toHaveLength(1536);

      // Verify embedding values are valid numbers
      embedding.forEach(value => {
        expect(typeof value).toBe('number');
        expect(value).not.toBeNaN();
        expect(value).toBeGreaterThan(-2);
        expect(value).toBeLessThan(2);
      });

      console.log('✅ OpenAI embedding generation verified');
    });

    test('should generate different embeddings for different texts', async () => {
      const text1 = 'The weather is sunny today';
      const text2 = 'I love programming with TypeScript';

      const embedding1 = await embeddingProvider.generateEmbedding(text1);
      const embedding2 = await embeddingProvider.generateEmbedding(text2);

      expect(embedding1).toHaveLength(1536);
      expect(embedding2).toHaveLength(1536);

      // Embeddings should be different
      const areDifferent = embedding1.some((val, idx) =>
        Math.abs(val - embedding2[idx]) > 0.001
      );
      expect(areDifferent).toBe(true);

      console.log('✅ Different embeddings for different texts verified');
    });

    test('should handle batch embedding generation', async () => {
      const texts = [
        'Universal AI Brain provides intelligent memory',
        'MongoDB Atlas Vector Search enables semantic similarity',
        'OpenAI embeddings convert text to vectors',
        'TypeScript provides type safety for JavaScript'
      ];

      const embeddings = await Promise.all(
        texts.map(text => embeddingProvider.generateEmbedding(text))
      );

      expect(embeddings).toHaveLength(4);
      embeddings.forEach(embedding => {
        expect(embedding).toHaveLength(1536);
        expect(Array.isArray(embedding)).toBe(true);
      });

      console.log('✅ Batch embedding generation verified');
    });
  });

  describe('💾 Memory Collection Operations', () => {
    test('should store and retrieve memory documents', async () => {
      const testMemory = {
        agentId: new ObjectId(),
        conversationId: `test_conversation_${Date.now()}`,
        content: 'User prefers coffee over tea and works best in the morning',
        memoryType: 'preference' as const,
        importance: 'high' as const,
        tags: ['preference', 'beverage', 'schedule'],
        metadata: {
          source: 'core_validation_test',
          framework: 'universal',
          sessionId: `test_session_${Date.now()}`
        },
        embedding: Array.from({ length: 1536 }, () => Math.random() - 0.5)
      };

      // Store memory using createMemory
      const result = await memoryCollection.createMemory(testMemory);
      expect(result._id).toBeDefined();

      // Retrieve memory using getMemory
      const retrieved = await memoryCollection.getMemory(result._id!);
      expect(retrieved).toBeDefined();
      expect(retrieved?.content).toBe(testMemory.content);
      expect(retrieved?.memoryType).toBe('preference');

      console.log('✅ Memory storage and retrieval verified');
    });

    test('should perform text search on stored memories', async () => {
      // Store test memories with real embeddings
      const testMemories = [
        'The user loves drinking coffee in the morning',
        'User prefers working during daylight hours',
        'The weather forecast shows rain tomorrow'
      ];

      const storedMemories = [];
      for (const content of testMemories) {
        const embedding = await embeddingProvider.generateEmbedding(content);
        const memory = {
          agentId: new ObjectId(),
          conversationId: `test_conversation_${Date.now()}`,
          content,
          memoryType: 'preference' as const,
          importance: 'medium' as const,
          tags: ['test'],
          metadata: {
            source: 'vector_search_test',
            framework: 'universal',
            sessionId: `test_session_${Date.now()}`
          },
          embedding
        };

        const result = await memoryCollection.createMemory(memory);
        storedMemories.push(result._id);
      }

      // Perform text search (since vector search requires Atlas Vector Search indexes)
      try {
        const searchResults = await memoryCollection.searchMemories('coffee drinking', {}, {
          limit: 3,
          sortBy: 'relevance'
        });

        console.log(`✅ Text search completed. Found ${searchResults.length} results`);
      } catch (error) {
        if (error.message.includes('text index') || error.message.includes('$text')) {
          console.log('⏭️ Text search skipped: Text indexes not available in test environment');
        } else {
          throw error;
        }
      }
    });
  });
});
