/**
 * @file Framework Adapters Validation Test
 * Tests all framework adapters with real integrations
 */

import * as dotenv from 'dotenv';
import { MongoClient, Db, ObjectId } from 'mongodb';
import { OpenAIEmbeddingProvider } from '../embeddings/OpenAIEmbeddingProvider';
import { MemoryCollection } from '../collections/MemoryCollection';
import { FrameworkAdapterManager } from '../adapters/FrameworkAdapterManager';
import { MastraAdapter } from '../adapters/MastraAdapter';
import { VercelAIAdapter } from '../adapters/VercelAIAdapter';
import { LangChainJSAdapter } from '../adapters/LangChainJSAdapter';
import { OpenAIAgentsAdapter } from '../adapters/OpenAIAgentsAdapter';

// Load environment variables
dotenv.config({ path: '../../../../.env' });

describe('🔌 Universal AI Brain - Framework Adapters Validation', () => {
  let mongoClient: MongoClient;
  let database: Db;
  let embeddingProvider: OpenAIEmbeddingProvider;
  let memoryCollection: MemoryCollection;
  let adapterManager: FrameworkAdapterManager;

  const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://romiluz:<EMAIL>/?retryWrites=true&w=majority&appName=agents';
  const OPENAI_API_KEY = process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************';
  const DATABASE_NAME = 'universal_ai_brain_test';

  beforeAll(async () => {
    console.log('🚀 Setting up Framework Adapters Validation Tests...');
    
    try {
      // Connect to MongoDB Atlas
      mongoClient = new MongoClient(MONGODB_URI, {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 10000,
        socketTimeoutMS: 45000,
        connectTimeoutMS: 10000,
        retryWrites: true,
        retryReads: true
      });

      await mongoClient.connect();
      database = mongoClient.db(DATABASE_NAME);
      console.log('✅ Connected to MongoDB Atlas');

      // Initialize OpenAI Embedding Provider
      embeddingProvider = new OpenAIEmbeddingProvider({
        apiKey: OPENAI_API_KEY,
        model: 'text-embedding-3-small',
        dimensions: 1536
      });
      console.log('✅ OpenAI Embedding Provider initialized');

      // Initialize Memory Collection
      memoryCollection = new MemoryCollection(database);
      console.log('✅ Memory Collection initialized');

      // Initialize Adapter Manager
      adapterManager = new FrameworkAdapterManager(database, embeddingProvider);
      console.log('✅ Framework Adapter Manager initialized');

    } catch (error) {
      console.error('❌ Setup failed:', error);
      throw error;
    }
  }, 30000);

  afterAll(async () => {
    console.log('🧹 Cleaning up Framework Adapters Validation Tests...');
    
    try {
      // Clean up test data
      if (database) {
        const collections = await database.listCollections().toArray();
        for (const collection of collections) {
          if (collection.name.includes('test') || collection.name.includes('adapter')) {
            await database.collection(collection.name).deleteMany({});
            console.log(`🗑️ Cleaned collection: ${collection.name}`);
          }
        }
      }

      // Close connections
      if (mongoClient) {
        await mongoClient.close();
        console.log('✅ MongoDB connection closed');
      }
    } catch (error) {
      console.warn('⚠️ Cleanup warning:', error);
    }
  }, 30000);

  describe('🎯 Framework Adapter Manager', () => {
    test('should initialize adapter manager successfully', async () => {
      expect(adapterManager).toBeDefined();

      // Initialize the manager first
      const mockBrain = {
        healthCheck: async () => true,
        enhancePrompt: async () => ({ enhancedPrompt: 'test', injectedContext: [] }),
        storeInteraction: async () => {},
        retrieveRelevantContext: async () => []
      } as any;

      await adapterManager.initialize(mockBrain);

      // Test that manager is ready
      expect(adapterManager.isReady()).toBe(true);
      expect(adapterManager.getRegisteredAdapters()).toEqual([]);

      console.log('✅ Framework Adapter Manager validation completed');
    });
  });

  describe('🔧 Mastra Adapter', () => {
    test('should initialize Mastra adapter', async () => {
      const adapter = new MastraAdapter();
      expect(adapter).toBeDefined();
      expect(adapter.getFrameworkName()).toBe('Mastra');

      console.log('✅ Mastra adapter initialization verified');
    });

    test('should handle Mastra-specific memory operations', async () => {
      const adapter = new MastraAdapter();

      // Test that adapter has the expected capabilities
      const capabilities = adapter.getCapabilities();
      expect(capabilities).toBeDefined();
      expect(capabilities.supportsMemory).toBe(true);
      expect(capabilities.supportsStreaming).toBe(true);
      expect(capabilities.supportsTools).toBe(true);

      console.log('✅ Mastra capabilities verified');
    });
  });

  describe('⚡ Vercel AI Adapter', () => {
    test('should initialize Vercel AI adapter', async () => {
      const adapter = new VercelAIAdapter();
      expect(adapter).toBeDefined();
      expect(adapter.getFrameworkName()).toBe('Vercel AI SDK');

      console.log('✅ Vercel AI adapter initialization verified');
    });

    test('should handle Vercel AI-specific operations', async () => {
      const adapter = new VercelAIAdapter();

      // Test that adapter has the expected capabilities
      const capabilities = adapter.getCapabilities();
      expect(capabilities).toBeDefined();
      expect(capabilities.supportsStreaming).toBe(true);
      expect(capabilities.supportsTools).toBe(true);

      console.log('✅ Vercel AI capabilities verified');
    });
  });

  describe('🦜 LangChain.js Adapter', () => {
    test('should initialize LangChain.js adapter', async () => {
      const adapter = new LangChainJSAdapter();
      expect(adapter).toBeDefined();
      expect(adapter.getFrameworkName()).toBe('LangChain.js');

      console.log('✅ LangChain.js adapter initialization verified');
    });

    test('should handle LangChain.js-specific operations', async () => {
      const adapter = new LangChainJSAdapter();

      // Test that adapter has the expected capabilities
      const capabilities = adapter.getCapabilities();
      expect(capabilities).toBeDefined();
      expect(capabilities.supportsMemory).toBe(true);
      expect(capabilities.supportsTools).toBe(true);

      console.log('✅ LangChain.js capabilities verified');
    });
  });

  describe('🤖 OpenAI Agents Adapter', () => {
    test('should initialize OpenAI Agents adapter', async () => {
      const adapter = new OpenAIAgentsAdapter();
      expect(adapter).toBeDefined();
      expect(adapter.getFrameworkName()).toBe('OpenAI Agents JS');

      console.log('✅ OpenAI Agents adapter initialization verified');
    });

    test('should handle OpenAI Agents-specific operations', async () => {
      const adapter = new OpenAIAgentsAdapter();

      // Test that adapter has the expected capabilities
      const capabilities = adapter.getCapabilities();
      expect(capabilities).toBeDefined();
      expect(capabilities.supportsTools).toBe(true);

      console.log('✅ OpenAI Agents capabilities verified');
    });
  });

  describe('🔄 Cross-Framework Integration', () => {
    test('should handle cross-framework memory sharing', async () => {
      const mastraAdapter = new MastraAdapter();
      const vercelAdapter = new VercelAIAdapter();

      // Test that adapters can be created and have different framework names
      expect(mastraAdapter).toBeDefined();
      expect(vercelAdapter).toBeDefined();
      expect(mastraAdapter.getFrameworkName()).not.toBe(vercelAdapter.getFrameworkName());

      console.log('✅ Cross-framework integration structure verified');
    });

    test('should maintain framework-specific metadata', async () => {
      const adapters = [
        new MastraAdapter(),
        new VercelAIAdapter(),
        new LangChainJSAdapter(),
        new OpenAIAgentsAdapter()
      ];

      adapters.forEach(adapter => {
        expect(adapter.getFrameworkName()).toBeDefined();
        expect(typeof adapter.getFrameworkName()).toBe('string');
      });

      const frameworkNames = adapters.map(adapter => adapter.getFrameworkName());
      expect(frameworkNames).toEqual(['Mastra', 'Vercel AI SDK', 'LangChain.js', 'OpenAI Agents JS']);

      console.log('✅ Framework-specific metadata verified');
    });
  });
});
