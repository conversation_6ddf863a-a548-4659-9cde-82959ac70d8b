/**
 * @file Real Credentials Integration Test
 * Comprehensive testing with actual MongoDB Atlas and OpenAI API
 */

import * as dotenv from 'dotenv';
import { MongoClient, Db } from 'mongodb';
import { UniversalAIBrain } from '../UniversalAIBrain';
import { OpenAIEmbeddingProvider } from '../embeddings/OpenAIEmbeddingProvider';
import { VectorSearchEngine } from '../intelligence/VectorSearchEngine';
import { SemanticMemoryEngine } from '../intelligence/SemanticMemoryEngine';
import { ContextInjectionEngine } from '../intelligence/ContextInjectionEngine';

// Load environment variables
dotenv.config({ path: '../../../../.env' });

describe('🧠 Universal AI Brain - Real Credentials Integration Tests', () => {
  let mongoClient: MongoClient;
  let database: Db;
  let brain: UniversalAIBrain;
  let embeddingProvider: OpenAIEmbeddingProvider;

  const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://romiluz:<EMAIL>/?retryWrites=true&w=majority&appName=agents';
  const OPENAI_API_KEY = process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************';
  const DATABASE_NAME = 'universal_ai_brain_test';

  beforeAll(async () => {
    console.log('🚀 Setting up Real Credentials Integration Tests...');
    
    try {
      // Connect to MongoDB Atlas
      mongoClient = new MongoClient(MONGODB_URI, {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 10000,
        socketTimeoutMS: 45000,
        connectTimeoutMS: 10000,
        retryWrites: true,
        retryReads: true
      });

      await mongoClient.connect();
      database = mongoClient.db(DATABASE_NAME);
      console.log('✅ Connected to MongoDB Atlas');

      // Initialize OpenAI Embedding Provider
      embeddingProvider = new OpenAIEmbeddingProvider({
        apiKey: OPENAI_API_KEY,
        model: 'text-embedding-3-small',
        dimensions: 1536
      });
      console.log('✅ OpenAI Embedding Provider initialized');

      // Initialize Universal AI Brain
      brain = new UniversalAIBrain({
        mongodb: {
          connectionString: MONGODB_URI,
          databaseName: DATABASE_NAME,
          collections: {
            tracing: 'ai_brain_tracing',
            memory: 'ai_brain_memory',
            context: 'ai_brain_context',
            metrics: 'ai_brain_metrics',
            audit: 'ai_brain_audit'
          }
        },
        intelligence: {
          embeddingModel: 'text-embedding-3-small',
          vectorDimensions: 1536,
          similarityThreshold: 0.7,
          maxContextLength: 4000
        },
        safety: {
          enableContentFiltering: true,
          enablePIIDetection: true,
          enableHallucinationDetection: true,
          enableComplianceLogging: true,
          safetyLevel: 'moderate'
        },
        monitoring: {
          enableRealTimeMonitoring: true,
          metricsRetentionDays: 30,
          alertingEnabled: true,
          dashboardRefreshInterval: 5000
        },
        selfImprovement: {
          enableAutomaticOptimization: true,
          learningRate: 0.01,
          optimizationInterval: 3600000,
          feedbackLoopEnabled: true
        }
      });

      await brain.initialize();
      console.log('✅ Universal AI Brain initialized');

    } catch (error) {
      console.error('❌ Setup failed:', error);
      throw error;
    }
  }, 30000);

  afterAll(async () => {
    console.log('🧹 Cleaning up Real Credentials Integration Tests...');
    
    try {
      // Clean up test data
      if (database) {
        const collections = await database.listCollections().toArray();
        for (const collection of collections) {
          if (collection.name.includes('test') || collection.name.includes('temp')) {
            await database.collection(collection.name).deleteMany({});
            console.log(`🗑️ Cleaned collection: ${collection.name}`);
          }
        }
      }

      // Close connections
      if (brain) {
        await brain.shutdown();
        console.log('✅ Universal AI Brain shutdown');
      }

      if (mongoClient) {
        await mongoClient.close();
        console.log('✅ MongoDB connection closed');
      }
    } catch (error) {
      console.warn('⚠️ Cleanup warning:', error);
    }
  }, 30000);

  describe('🔌 Core Connectivity Tests', () => {
    test('should connect to MongoDB Atlas successfully', async () => {
      expect(mongoClient).toBeDefined();
      expect(database).toBeDefined();
      
      // Test database operations
      const testCollection = database.collection('connectivity_test');
      const testDoc = { test: true, timestamp: new Date() };
      
      const result = await testCollection.insertOne(testDoc);
      expect(result.insertedId).toBeDefined();
      
      const retrieved = await testCollection.findOne({ _id: result.insertedId });
      expect(retrieved).toBeDefined();
      expect(retrieved?.test).toBe(true);
      
      await testCollection.deleteOne({ _id: result.insertedId });
      console.log('✅ MongoDB Atlas connectivity verified');
    });

    test('should generate embeddings with OpenAI API', async () => {
      const testText = 'This is a test for Universal AI Brain embedding generation';
      
      const embedding = await embeddingProvider.generateEmbedding(testText);
      
      expect(embedding).toBeDefined();
      expect(embedding.values).toHaveLength(1536);
      expect(embedding.model).toBe('text-embedding-3-small');
      expect(embedding.dimensions).toBe(1536);
      
      // Verify embedding values are valid numbers
      embedding.values.forEach(value => {
        expect(typeof value).toBe('number');
        expect(value).not.toBeNaN();
      });
      
      console.log('✅ OpenAI embedding generation verified');
    });
  });

  describe('🧠 Universal AI Brain Core Tests', () => {
    test('should initialize all core components', async () => {
      expect(brain).toBeDefined();
      expect(brain.isInitialized()).toBe(true);
      
      // Test core components are accessible
      const memoryEngine = brain.getSemanticMemoryEngine();
      const contextEngine = brain.getContextInjectionEngine();
      const vectorEngine = brain.getVectorSearchEngine();
      
      expect(memoryEngine).toBeDefined();
      expect(contextEngine).toBeDefined();
      expect(vectorEngine).toBeDefined();
      
      console.log('✅ Universal AI Brain core components verified');
    });

    test('should store and retrieve memories with real embeddings', async () => {
      const testMemory = {
        content: 'The user prefers coffee over tea and likes to work in the morning',
        metadata: {
          type: 'preference' as const,
          importance: 0.8,
          confidence: 0.9,
          source: 'integration_test',
          framework: 'universal',
          sessionId: `test_session_${Date.now()}`,
          tags: ['preference', 'beverage', 'schedule'],
          relationships: [],
          accessCount: 0,
          lastAccessed: new Date(),
          created: new Date(),
          updated: new Date()
        }
      };

      const memoryEngine = brain.getSemanticMemoryEngine();
      
      // Store memory with real embedding
      const memoryId = await memoryEngine.storeMemory(testMemory.content, testMemory.metadata);
      expect(memoryId).toBeDefined();
      expect(typeof memoryId).toBe('string');
      
      // Retrieve memories using semantic search
      const searchResults = await memoryEngine.retrieveRelevantMemories(
        'What does the user like to drink?',
        { limit: 5, minImportance: 0.5 }
      );
      
      expect(searchResults).toBeDefined();
      expect(Array.isArray(searchResults)).toBe(true);
      
      console.log(`✅ Memory storage and retrieval verified. Found ${searchResults.length} relevant memories`);
    });
  });

  describe('🔍 Vector Search Integration Tests', () => {
    test('should perform hybrid vector + text search', async () => {
      const vectorEngine = brain.getVectorSearchEngine();
      
      // Store test documents with embeddings
      const testDocs = [
        'Universal AI Brain provides intelligent memory for AI frameworks',
        'MongoDB Atlas Vector Search enables semantic similarity matching',
        'OpenAI embeddings convert text into high-dimensional vectors'
      ];

      const collection = database.collection('vector_search_test');
      
      for (const doc of testDocs) {
        const embedding = await embeddingProvider.generateEmbedding(doc);
        await collection.insertOne({
          content: doc,
          embedding: embedding,
          metadata: { type: 'test', created: new Date() }
        });
      }

      // Perform vector search
      const searchQuery = 'AI memory and intelligence';
      const queryEmbedding = await embeddingProvider.generateEmbedding(searchQuery);
      
      const results = await vectorEngine.performHybridSearch(
        collection.collectionName,
        queryEmbedding.values,
        searchQuery,
        { limit: 3, minScore: 0.1 }
      );

      expect(results).toBeDefined();
      expect(Array.isArray(results)).toBe(true);
      
      console.log(`✅ Vector search verified. Found ${results.length} relevant documents`);
    });
  });
});
